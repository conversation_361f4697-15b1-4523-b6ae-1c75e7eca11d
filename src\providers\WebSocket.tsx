'use client';

import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { CHAT_CONSTANTS } from '@/constants/chatConstants';
import { useAppSelector } from '@/store/hooks';
import { getWebSocketBaseURL } from '../components/globalComponents/ChatWithShasa/chatURL';
import { selectCurrentUser } from '@/slices/authSlice';

type ChatMessage = {
  id?: any;
  cards?: any[];
  sender: 'human' | 'ai';
  text?: string;
  message_type?: string;
  timestamp: string;
  messageId?: string;
};

type ErrorMessage = {
  status: string;
  message: string;
};

type WebSocketContextType = {
  isConnected: boolean;
  sendMessage: (msg: string) => boolean;
  messages: ChatMessage[];
  errorMessages: ErrorMessage[];
  isBotThinking: boolean;
};

const WebSocketContext = createContext<WebSocketContextType | null>(null);

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [errorMessages, setErrorMessages] = useState<ErrorMessage[]>([]);
  const [isBotThinking, setIsBotThinking] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const messageQueue = useRef<string[]>([]);

  const thread = useAppSelector(state => state.thread);
  const currentUser = useAppSelector(selectCurrentUser);

  useEffect(() => {
    if (!thread?.thread_id) return;

    // Close old socket if exists
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    const baseUrl = getWebSocketBaseURL();
    const wsUrl = `${baseUrl}${CHAT_CONSTANTS.API_ENDPOINTS.WEBSOCKET_PATH}${thread.thread_id}`;
    console.log('🔌 Connecting to WebSocket:', wsUrl);

    const socket = new WebSocket(wsUrl);
    wsRef.current = socket;

    socket.onopen = () => {
      console.log('✅ WebSocket connected');
      setIsConnected(true);

      // Flush queued messages
      messageQueue.current.forEach(msg => socket.send(msg));
      messageQueue.current = [];
    };

    socket.onmessage = (event: MessageEvent<string>) => {
      try {
        const res = JSON.parse(event.data);

        // Detect "Shasa thinking" messages
        if (
          typeof res.message === 'string' &&
          res.message.includes('Shasa thinking')
        ) {
          setIsBotThinking(true);
          return;
        }
        setIsBotThinking(false);

        if (
          res.type === CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.AGENT_RESPONSE
        ) {
          setMessages(prev => [
            ...prev,
            {
              sender: 'ai',
              text: res.content || '',
              timestamp: res.timestamp || new Date().toISOString(),
              messageId: res.id,
            },
          ]);
        } else if (res.message) {
          setErrorMessages(prev => [
            ...prev,
            { status: 'error', message: res.message || 'Unknown error' },
          ]);
          console.log('ℹ️ Unhandled WS message:', res);
        }
      } catch (err) {
        setErrorMessages(prev => [
          ...prev,
          { status: 'error', message: 'Failed to parse WebSocket message' },
        ]);
        console.error('Error parsing WS message:', err);
      }
    };

    socket.onerror = err => {
      console.error('🔥 WebSocket error:', err);
    };

    socket.onclose = () => {
      console.log('❌ WebSocket disconnected');
      setIsConnected(false);
      setIsBotThinking(false);
    };

    // eslint-disable-next-line consistent-return
    return () => {
      socket.close();
    };
  }, [thread?.thread_id, currentUser]);

  const sendMessage = (msg: string): boolean => {
    const payload = JSON.stringify({
      query: msg,
      thread_id: thread?.thread_id,
      message_type: 'text',
    });

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(payload);
    } else {
      console.warn('⚠️ WebSocket not ready, queuing message');
      messageQueue.current.push(payload);
    }

    // Add human message locally
    setMessages(prev => [
      ...prev,
      {
        sender: 'human',
        text: msg,
        timestamp: new Date().toISOString(),
        message_type: 'text',
      },
    ]);

    return true;
  };

  return (
    <WebSocketContext.Provider
      value={{
        isConnected,
        sendMessage,
        messages,
        errorMessages,
        isBotThinking,
      }}
    >
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = (): WebSocketContextType => {
  const ctx = useContext(WebSocketContext);
  if (!ctx) {
    throw new Error('useWebSocket must be used inside WebSocketProvider');
  }
  return ctx;
};
