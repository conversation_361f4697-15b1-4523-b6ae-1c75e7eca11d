'use client';

import {
  useEffect,
  useState,
  useRef,
  useCallback,
  useLayoutEffect,
} from 'react';
import { useDisclosure } from '@heroui/react';
import { SortIcon } from '@/components/icons';
import { useSession } from 'next-auth/react';
import RecommendationCard from './RecommendationCard';
import Cookies from 'js-cookie';

import { useDispatch, useSelector } from 'react-redux';
import {
  setRecommendations,
  applyFilters,
  selectFilteredRecommendations,
} from '@/slices/recommendationSlice';
import { useGetRecommendationsMutation } from '@/services/recommendationApi';
import RecommendationFilter from '@/components/globalComponents/Filter/RecommendationFilter';

const Recommendation = () => {
  // Local UI states
  const [visibleCards, setVisibleCards] = useState(3);
  const [currentStartIndex, setCurrentStartIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const { data: session } = useSession();

  // UI hooks
  const { isOpen, onOpen, onClose } = useDisclosure();

  // DOM refs
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const dispatch = useDispatch();

  // RTK Query
  const [getRecommendations, { isLoading, isError }] =
    useGetRecommendationsMutation();

  // Redux selectors
  const recommendations = useSelector(selectFilteredRecommendations);

  /**
   * Fetch recommendations from API using RTK Query
   */
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getRecommendations({
          page: 1,
          pageSize: 10,
        }).unwrap();

        const data = res.detail.data.recommendations;

        if (data) {
          dispatch(setRecommendations(data));

          // Optional: persist countries in cookie
          Cookies.set(
            'locationCountries',
            JSON.stringify(data.map(r => r.country)),
            { expires: 7 }
          );
        }
      } catch (err) {
        console.error('Failed to fetch recommendations:', err);
      }
    };

    fetchData();
  }, [session, dispatch, getRecommendations]);

  /**
   * Responsive card calculation
   */
  const calculateVisibleCards = useCallback(
    (retryCount = 0) => {
      const windowWidth = window.innerWidth;
      let newVisibleCards = 3;

      if (windowWidth < 640) {
        newVisibleCards = Math.min(2, recommendations.length);
      } else if (windowWidth < 1024) {
        newVisibleCards = Math.min(3, recommendations.length);
      } else if (containerRef.current && headerRef.current) {
        const containerHeight = containerRef.current.clientHeight;
        const headerHeight = headerRef.current.clientHeight;

        if (containerHeight === 0 || headerHeight === 0) {
          if (retryCount < 5) {
            retryTimeoutRef.current = setTimeout(
              () => calculateVisibleCards(retryCount + 1),
              100 * (retryCount + 1)
            );
          }
          return;
        }

        const availableHeight = containerHeight - headerHeight - 24 - 16 - 4; // remove paddings + margins
        const estimatedCardHeight = 120;
        const cardGap = 8;

        const maxPossibleCards = Math.floor(
          (availableHeight + cardGap) / (estimatedCardHeight + cardGap)
        );

        newVisibleCards = Math.max(
          1,
          Math.min(maxPossibleCards, recommendations.length, 5)
        );
      }

      setVisibleCards(prev => {
        if (prev !== newVisibleCards) {
          setCurrentStartIndex(0);
          return newVisibleCards;
        }
        return prev;
      });
    },
    [recommendations.length]
  );

  useLayoutEffect(() => {
    const timer = setTimeout(() => {
      calculateVisibleCards();
    }, 0);
    return () => clearTimeout(timer);
  }, [calculateVisibleCards]);

  /**
   * Handle resize
   */
  useEffect(() => {
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      resizeTimeoutRef.current = setTimeout(() => {
        calculateVisibleCards();
      }, 100);
    };

    if (containerRef.current && typeof ResizeObserver !== 'undefined') {
      resizeObserverRef.current = new ResizeObserver(() => {
        calculateVisibleCards();
      });
      resizeObserverRef.current.observe(containerRef.current);
    }

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setTimeout(() => calculateVisibleCards(), 50);
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      if (resizeTimeoutRef.current) clearTimeout(resizeTimeoutRef.current);
      if (retryTimeoutRef.current) clearTimeout(retryTimeoutRef.current);
      if (resizeObserverRef.current) resizeObserverRef.current.disconnect();
    };
  }, [calculateVisibleCards]);

  /**
   * Auto-cycle recommendations
   */
  useEffect(() => {
    if (recommendations.length <= visibleCards || isHovered) {
      // Explicitly return undefined so ESLint is happy
      return undefined;
    }

    const interval = setInterval(() => {
      setCurrentStartIndex(prev => {
        const maxStartIndex = recommendations.length - visibleCards;
        return prev >= maxStartIndex ? 0 : prev + 1;
      });
    }, 4000);

    // Proper cleanup function
    return () => {
      clearInterval(interval);
    };
  }, [visibleCards, recommendations.length, isHovered]);

  const displayedRecommendations =
    recommendations.length > 4
      ? recommendations.slice(
          currentStartIndex,
          currentStartIndex + visibleCards
        )
      : recommendations;

  useEffect(() => {
    const timer = setTimeout(() => {
      calculateVisibleCards();
    }, 100);
    return () => clearTimeout(timer);
  }, [displayedRecommendations.length, calculateVisibleCards]);

  /**
   * Filter handlers (Redux-driven now)
   */

  const applyFilterData = () => {
    dispatch(applyFilters());
    onClose();
  };

  return (
    <div
      ref={containerRef}
      className="bg-white h-full px-4 py-2 rounded-xl flex flex-col overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        ref={headerRef}
        className="flex flex-row items-center justify-between flex-shrink-0"
      >
        <p className="text-lg font-bold">Recommendation</p>
        <div className="flex flex-row items-center gap-2 cursor-pointer">
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <span
            role="button"
            tabIndex={0}
            className="text-sm font-medium text-default-Secondary"
            onClick={onOpen}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
              }
            }}
          >
            Filter
          </span>
        </div>
      </div>

      <div className="flex-1 flex flex-col justify-between min-h-0 overflow-hidden">
        {isLoading && <p>Loading...</p>}
        {isError && <p>Error loading recommendations</p>}

        {!isLoading && recommendations.length === 0 && <p>No data</p>}

        <RecommendationCard
          recommendations={displayedRecommendations.map(item => ({
            ...item,
            country: item.country ?? '',
            image_url: item.image_url ?? item.image ?? '',
            theme: item.theme ?? '',
          }))}
          showViewAll
        />
      </div>

      {/* this is the RecommendationFilter use this insted of modal */}
      <RecommendationFilter
        open={isOpen}
        onClose={onClose}
        onApply={applyFilterData}
      />
    </div>
  );
};

export default Recommendation;
