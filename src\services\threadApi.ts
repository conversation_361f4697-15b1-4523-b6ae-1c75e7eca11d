// src/services/threadApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

interface ThreadGenerateRequest {
  _new: boolean;
}

interface ThreadGenerateResponse {
  detail: {
    status: string;
    message: string;
    data: {
      thread_id: string;
      expires_at: string;
      created_at: string;
      reused: boolean;
      usage: string;
    };
  };
}

interface ChatRequestPayload {
  messages: [
    {
      role: string;
      content: string;
    },
  ];
  current_input: string;
}

interface SuggestionResponse {
  detail: {
    status: string;
    message: string;
    data: {
      suggestions: string[];
    };
  };
}

export const threadApi = createApi({
  reducerPath: 'threadApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: async headers => {
      headers.set('accept', 'application/json');
      headers.set('Content-Type', 'application/json');

      const session = await getSession();
      const token = session?.accessToken as string | undefined;
      const guestId = session?.user?.guest_id as string | undefined;

      if (token) headers.set('authorization', `Bearer ${token}`);
      if (guestId) headers.set('x-guest-id', guestId);

      return headers;
    },
  }),
  tagTypes: ['Thread'],
  endpoints: builder => ({
    generateThread: builder.mutation<
      ThreadGenerateResponse,
      ThreadGenerateRequest
    >({
      query: body => ({
        url: '/api/v1/thread/generate',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Thread'],
    }),
    getSuggestion: builder.mutation<SuggestionResponse, ChatRequestPayload>({
      query: body => ({
        url: 'api/v1/suggestions',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Thread'],
    }),
  }),
});

export const { useGenerateThreadMutation, useGetSuggestionMutation } =
  threadApi;
