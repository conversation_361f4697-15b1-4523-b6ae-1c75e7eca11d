'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@heroui/react';
// import { useSelector } from 'react-redux';
import { Input } from '@/components/ui/input';
import LocationCard from './LocationCard';
import FlightCard from './FlightCard';
import { useGetSuggestionMutation } from '@/services/threadApi';
import { useWebSocket } from '@/providers/WebSocket';
// import {
//   selectStartLocation,
//   selectDestination,
//   selectDestinations,
//   selectPickedCoords,
// } from '@/slices/locationSlice';
// import { ItineryInputs as selectItinerary } from '@/slices/chatRequest';

// interface Message {
//   id: string;
//   type: 'user' | 'assistant';
//   content: string;
//   timestamp: string;
//   cards?: {
//     type: 'location' | 'flight';
//     data: any;
//   }[];
// }
type SuggestionResponse = {
  detail?: {
    data?: {
      suggestions?: string[];
    };
  };
};

const ChatWithShasa = () => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const { isConnected, sendMessage, messages, errorMessages, isBotThinking } =
    useWebSocket();
  const [getSuggestion] = useGetSuggestionMutation();
  // const startLocation = useSelector(selectStartLocation);
  // const destination = useSelector(selectDestination);
  // const destinations = useSelector(selectDestinations);
  // const pickedCoords = useSelector(selectPickedCoords);
  // const ItineryInputs = useSelector(selectItinerary);

  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // ✅ show alerts when socket connects/disconnects
  useEffect(() => {
    // if (isConnected) {
    //   alert('✅ WebSocket connected');
    // } else {
    //   alert('❌ WebSocket disconnected');
    // }
    console.log(isConnected);
  }, [isConnected]);

  // ✅ show alerts for new error messages
  useEffect(() => {
    if (errorMessages && errorMessages.length > 0) {
      const lastError = errorMessages[errorMessages.length - 1];
      // eslint-disable-next-line no-alert
      alert(`🔥 Error: ${lastError.message}`);
    }
  }, [errorMessages]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!inputValue || inputValue.trim().length <= 2) {
      setSuggestions([]);
      return;
    }

    if (debounceRef.current) clearTimeout(debounceRef.current);

    debounceRef.current = setTimeout(() => {
      const fetchSuggestions = async () => {
        try {
          const response: SuggestionResponse = await getSuggestion({
            messages: [
              {
                role: 'human',
                content: inputValue,
              },
            ],
            current_input: inputValue,
          }).unwrap();
          setSuggestions(response?.detail?.data?.suggestions || []);
        } catch (error) {
          setSuggestions([]);
        }
      };

      fetchSuggestions();
    }, 300);

    // eslint-disable-next-line consistent-return
    return () => {
      if (debounceRef.current) clearTimeout(debounceRef.current);
    };
  }, [inputValue]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    setInputValue('');
    setIsTyping(true);

    try {
      sendMessage(inputValue);
    } catch (error) {
      // error already captured by provider, no need to do anything here
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // const quickActions = [
  //   'Winter',
  //   'Explore local cuisines',
  //   'Visit historical landmarks',
  //   'Discover hidden gems',
  //   'Experience nightlife',
  // ];

  const containerRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<number>(0);

  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new ResizeObserver(entries => {
      entries.forEach(entry => {
        const newWidth = entry.contentRect.width;
        setWidth(newWidth);
      });
    });

    observer.observe(containerRef.current);
    // eslint-disable-next-line consistent-return
    return () => observer.disconnect();
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-[calc(100vh-275px)] bg-gradient-to-br from-[#C084FC] via-[#8B5CF6] to-[#17074C] rounded-xl p-4 sm:p-6 flex flex-col relative overflow-hidden"
    >
      {/* … header and messages unchanged … */}

      {/* Messages */}
      <div
        ref={chatContainerRef}
        className="flex-1 custom-blur hide-scrollbar rounded-lg p-3 overflow-y-auto space-y-4 mb-4 relative z-10"
        style={{ maxHeight: 'calc(100% - 150px)' }}
      >
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={`${message.id}_${index}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              // className={`flex ${message.sender === 'human' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`flex ${message.sender === 'human' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`flex items-start max-w-[95%] ${
                    message.sender === 'human' ? 'flex-row-reverse ml-auto' : ''
                  }`}
                >
                  <div className="flex flex-col relative">
                    {/* Curved Bubble Tail */}
                    <svg
                      className={`absolute w-3 h-3 ${
                        message.sender === 'human'
                          ? 'top-[4.1px] -right-[12px]'
                          : 'top-[4.1px] -left-[12px]'
                      }`}
                      viewBox="0 0 10 10"
                    >
                      <path
                        d={
                          message.sender === 'human'
                            ? 'M0,0 Q10,3 0,10 Z' // tail pointing left
                            : 'M10,0 Q0,5 10,10 Z' // tail pointing right
                        }
                        fill={
                          message.sender === 'human'
                            ? 'rgba(0, 0, 0, 0.3)'
                            : 'rgba(255,255,255,0.1)'
                        }
                      />
                    </svg>

                    {/* Message Bubble */}
                    <div
                      className={`px-4 py-3 mt-1 backdrop-blur-lg ${
                        message.sender === 'human'
                          ? 'bg-black/30 text-white ml-auto rounded-lg rounded-tr-none'
                          : 'bg-white/10 text-white rounded-lg rounded-tl-none'
                      }`}
                    >
                      {/* {message.sender === 'assistant' && (
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-white text-sm font-semibold">
                            Shasa
                          </span>
                          <span className="text-white/60 text-xs">
                            AI travel assistant
                          </span>
                        </div>
                      )} */}
                      <p className="text-sm leading-relaxed whitespace-pre-wrap">
                        {message.text}
                      </p>

                      <div className="flex justify-end mt-2">
                        <span className="text-xs text-white/60">
                          {message.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`flex mt-3 ${message.sender === 'human' ? 'justify-end' : 'justify-start'}`}
              >
                {/* Render cards if present */}
                {message.cards && message?.cards?.length > 0 && (
                  <div
                    //  className="mt-4 space-y-3 "
                    className={`flex items-start w-full max-w-[95%] overflow-x-scroll  hide-scrollbar${
                      message.sender === 'human'
                        ? 'flex-row-reverse ml-auto'
                        : ''
                    }`}
                  >
                    {message?.cards?.map((card: any) => (
                      <div
                        key={`${message.id}-card-${card.type}-${card.data.id || card.data.name}`}
                        className="w-full space-x-3"
                      >
                        {card.type === 'location' && (
                          <div className="w-full">
                            <LocationCard location={card.data} />
                          </div>
                        )}
                        {card.type === 'flight' && (
                          <FlightCard
                            flight={card}
                            onSelectClick={() => {
                              // Handle select flight action
                            }}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
          <div>
            {isBotThinking && (
              <div className="loader text-center">
                <span>Shasa is thinking… 🤔</span>
                {/* Or show a spinner component */}
              </div>
            )}
          </div>
        </AnimatePresence>

        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            {/* typing indicator */}
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>
      <div className="mb-3 relative z-10 ">
        <div className="flex space-x-2 overflow-x-auto hide-scrollbar pb-2">
          {suggestions?.map((action, index) => (
            <Button
              key={`${index + 1}`} // unique key
              variant="bordered"
              size="sm"
              className="bg-transparent border-white/20 text-white hover:bg-white/5 hover:text-white whitespace-nowrap flex-shrink-0 backdrop-blur-sm rounded-full h-[29px] text-sm font-medium"
              onClick={() => {
                setInputValue(action); // use string property
                handleSendMessage();
              }}
            >
              {action} {/* render string property, not object */}
            </Button>
          ))}
        </div>
      </div>
      {/* … rest of your input area … */}
      <div className="flex items-center space-x-3 flex-shrink-0 relative z-10">
        <div className="flex-1 relative">
          <Input
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me anything travel related..."
            className="bg-white outline-[2px] outline-white/20 text-black placeholder:text-[#AEAEB2] rounded-full pr-12 h-10 backdrop-blur-sm"
          />
        </div>
        <Button onPress={handleSendMessage}>Send</Button>
      </div>
    </div>
  );
};

export default ChatWithShasa;
