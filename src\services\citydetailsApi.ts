// src/services/cityApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

interface CityDetailsResponse {
  city: string;
  country_name: string;
  [key: string]: any;
}

export const cityApi = createApi({
  reducerPath: 'cityApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_GEO_API_ENDPOINT,
    prepareHeaders: async headers => {
      headers.set('accept', 'application/json');
      headers.set('Content-Type', 'application/json');
      headers.set(
        'x-api-key',
        'nxvoy_country_details_dev_dRWRG7T2okxg3lSmtlfbbagU2RmUNG1W'
      );

      const session = await getSession();
      const token = session?.accessToken as string | undefined;
      const guestId = session?.user?.guest_id as string | undefined;

      if (token) headers.set('authorization', `Bearer ${token}`);
      if (guestId) headers.set('x-guest-id', guestId);

      return headers;
    },
  }),
  endpoints: builder => ({
    getCityDetails: builder.query<CityDetailsResponse, void>({
      query: () => ({
        url: '/api/v1/city-details',
        method: 'GET',
      }),
    }),
  }),
});

export const { useGetCityDetailsQuery, useLazyGetCityDetailsQuery } = cityApi;
