// src/services/travelStyleApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

interface TravelStyle {
  style: string;
  tags: string[];
}

interface TravelStyleResponse {
  message: string;
  detail: {
    data: TravelStyle[];
  };
}

export const travelStyleApi = createApi({
  reducerPath: 'travelStyleApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: async headers => {
      headers.set('accept', 'application/json');
      headers.set('Content-Type', 'application/json');

      const session = await getSession();
      const token = session?.accessToken as string | undefined;
      const guestId = session?.user?.guest_id as string | undefined;

      if (token) headers.set('authorization', `Bearer ${token}`);
      if (guestId) headers.set('x-guest-id', guestId);

      return headers;
    },
  }),
  tagTypes: ['TravelStyle'],
  endpoints: builder => ({
    getTravelStyleSuggestions: builder.query<TravelStyleResponse, void>({
      query: () => ({
        url: '/api/v1/activities/suggestions/travel-style',
        method: 'GET',
      }),
      providesTags: ['TravelStyle'],
    }),
  }),
});

export const { useGetTravelStyleSuggestionsQuery } = travelStyleApi;
