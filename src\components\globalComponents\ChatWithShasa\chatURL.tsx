const agentApiBaseURL = process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT;

export const getWebSocketBaseURL = () => {
  if (!agentApiBaseURL) return null;

  const baseUrl = agentApiBaseURL
    .replace('http://', 'ws://')
    .replace('https://', 'wss://');
  return baseUrl;
};

export const generateTimestamp = (): string => {
  const now = new Date();

  const pad = (n: number) => n.toString().padStart(2, '0');

  const year = now.getFullYear();
  const month = pad(now.getMonth() + 1); // Months are 0-based
  const day = pad(now.getDate());
  const hours = pad(now.getHours());
  const minutes = pad(now.getMinutes());
  const seconds = pad(now.getSeconds());
  const microseconds = (now.getMilliseconds() * 1000)
    .toString()
    .padStart(6, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}`;
};
