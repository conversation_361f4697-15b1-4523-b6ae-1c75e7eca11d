// src/slices/threadSlice.ts
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

interface ThreadState {
  thread_id: string | null;
  expires_at: string | null;
  created_at: string | null;
  reused: boolean;
  usage: string | null;
}

type GroupedThreads = Record<
  string,
  Array<{
    thread_id: string;
    thread_name: string;
    updated_at: string;
    created_at: string;
    message_count: number;
  }>
>;

interface ChatThreadState {
  thread_id: string | null;
  currentChatPageThreadId: string | null;
  currentThreadName: string | null;
  history: boolean;
  newThread: boolean;
  showMoreFlightsOption: boolean;
  allFlights: any;
  tripType: string;
  chatResult: any;
  chatHistory: GroupedThreads;
  chatHistoryLoaded: boolean;
  isRefreshingHistory: boolean;
  shouldRefreshHistory: boolean;
}

// ✅ Load persisted thread from localStorage
const getStoredThread = (): ThreadState | null => {
  if (typeof window === 'undefined') return null;
  const stored = localStorage.getItem('thread');
  if (!stored) return null;
  try {
    return JSON.parse(stored);
  } catch (err) {
    console.error('Failed to parse stored thread', err);
    return null;
  }
};

const initialThread = getStoredThread();

const initialState: ChatThreadState = {
  thread_id: initialThread?.thread_id || null,
  currentChatPageThreadId: '',
  currentThreadName: '',
  history: false,
  newThread: false,
  showMoreFlightsOption: false,
  allFlights: {},
  tripType: '',
  chatResult: {},
  chatHistory: {},
  chatHistoryLoaded: false,
  isRefreshingHistory: false,
  shouldRefreshHistory: false,
};

const threadSlice = createSlice({
  name: 'thread',
  initialState,
  reducers: {
    setThread(state, action: PayloadAction<ThreadState>) {
      const newThread = action.payload;
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('thread', JSON.stringify(newThread));
      }
      return { ...state, ...newThread };
    },
    resetThread(state) {
      // Remove from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('thread');
      }
      return {
        ...state,
        thread_id: null,
        expires_at: null,
        created_at: null,
        reused: false,
        usage: null,
      };
    },
    setCurrentChatPageThreadId: (state, action: PayloadAction<string>) => {
      state.currentChatPageThreadId = action.payload;
    },
    clearCurrentChatPageThreadId: state => {
      state.currentChatPageThreadId = '';
    },
    updateCurrentThreadInfo: (
      state,
      action: PayloadAction<Partial<ChatThreadState>>
    ) => {
      return { ...state, ...action.payload };
    },
    clearChatThreadState: state => {
      state.currentChatPageThreadId = '';
      state.currentThreadName = '';
      state.history = false;
      state.newThread = false;
      state.showMoreFlightsOption = false;
      state.allFlights = {};
      state.tripType = '';
      state.chatResult = {};
    },
  },
});

export const {
  setThread,
  resetThread,
  setCurrentChatPageThreadId,
  clearCurrentChatPageThreadId,
  updateCurrentThreadInfo,
  clearChatThreadState,
} = threadSlice.actions;

export default threadSlice.reducer;
