// export const THREAD_COOKIE_NAME = "nxvoy_thread_id";
export const NEW_THREAD_DEFAULT_NAME = 'New Chat';

export const CHAT_CONSTANTS = {
  SOCKET_STATUS: {
    CONNECTED: 'connected' as const,
    DISCONNECTED: 'disconnected' as const,
  },

  WEBSOCKET_MESSAGE_TYPES: {
    PROCESSING: 'processing',
    AGENT_EVENT: 'agent_event',
    THREAD_NAME_UPDATED: 'thread_name_updated',
    TOKEN_STREAM: 'token_stream',
    FLIGHT_DATA: 'flight_data',
    TOKEN_STREAM_START: 'token_stream_start',
    TOKEN_STREAM_END: 'token_stream_end',
    AGENT_RESPONSE: 'agent_response',
    TOOL_RESULT: 'tool_result',
    COMPLETED: 'completed',
  },

  DEFAULT_MESSAGES: {
    SHASA_THINKING: 'Shasa thinking... 🤔',
    ERROR_GENERAL:
      "Oops! Something went wrong on my end. Try again in a bit — I'm on it!",
    CONNECTION_CLOSED: 'Connection is closed, Start new chat.',
    TOKEN_EXPIRED: 'Token Expire, search flight again from chat',
    FEEDBACK_SUCCESS: 'Thank you for your feedback!',
    FEEDBACK_ERROR: 'Feedback not submitted',
  },

  SUGGESTED_MESSAGES: [
    'Where to enjoy a Seine River cruise?',
    'What are the best neighborhoods to explore?',
    'Book flight ticket Chennai to London?',
  ],

  DEFAULT_ERROR_IMAGE:
    'https://storage.googleapis.com/nxvoytrips-img/HomeImgs/ErrorModal/shash-I.png',

  SHASA_AVATAR:
    'https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA-face-AI.svg',

  SHASA_VIDEO:
    'https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA%20face%20FFFFFF.mp4',

  API_ENDPOINTS: {
    THREAD_GENERATE: '/api/v1/thread/generate?_new=true',
    MESSAGE_FEEDBACK: '/api/v1/chat-history/message-feedback',
    CHAT_MESSAGES: '/api/v1/chat-history/messages',
    WEBSOCKET_PATH: '/api/v1/thread/ws/',
  },

  RETRY_CONFIG: {
    MAX_RETRIES: 3,
    BASE_WAIT_TIME: 1000,
  },

  UI_CONFIG: {
    MAX_FLIGHTS_DISPLAYED: 3,
    SCROLL_BEHAVIOR: 'smooth' as const,
    TOAST_DURATION: 2000,
  },
};
